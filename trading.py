
import datetime
from xtquant import xtdata
import tushare as ts
token='2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro=ts.pro_api(token)
import os
from joblib import Parallel, delayed
import warnings
warnings.filterwarnings("ignore")

# dividend_type: 复权类型, none, front, back, front_ratio, back_ratio
dividend_type = 'front'
# period: 数据周期, 1m, 5m, 15m, 30m, 1h, 1d, tick
period = '1d'

def download_data(stock, period):
    try:
        xtdata.download_history_data(stock_code = stock, period = period)
    except:
        print(stock, 'data failed')

# 1 获取股票列表
print('获取股票列表...')
df = pro.daily_basic(ts_code='', trade_date='20250829')
# 获取股票基本信息，包括股票名称
stock_info = pro.stock_basic(exchange='', list_status='L')
stock_list = df['ts_code'].tolist()
stock_dict = dict(zip(stock_info['ts_code'], stock_info['name']))
print('获取股票列表完成')

# 2 下载数据
print('下载数据...')
res = Parallel(n_jobs=4)(delayed(download_data)(stock, period) for stock in stock_list)   
print('下载数据完成') 

# 3 整理数据，将下载的加密数据整理成DataFrame保存成本地.csv文件
print('整理数据...')
folder_path = 'stock-trading-data-pro'
if not os.path.exists(folder_path): os.makedirs(folder_path)
for i, stock in enumerate(stock_list):
    # 隔50个打印下进度
    if i%50 == 0: print(i,stock)
    data = xtdata.get_local_data(field_list=['time', 'open','close','high','low','volume',
                                             'amount','settelementPrice', 'openInterest',
                                             'preClose', 'suspendFlag'],
                                 stock_list=[stock],
                                 period=period,
                                 dividend_type=dividend_type)
    df = data[stock]
    try:
        df['datetime'] = df['time'].apply(lambda x: datetime.datetime.fromtimestamp(x/1000.0))
        df.index = df['datetime']
        df = df[['open','close','high','low','volume','amount','preClose']]
        
        # 将股票代码转换为bj430017这样的格式（添加交易所前缀）
        if stock.endswith('.SZ'):
            filename = 'sz' + stock.replace('.SZ', '')
        elif stock.endswith('.SH'):
            filename = 'sh' + stock.replace('.SH', '')
        elif stock.endswith('.BJ'):
            filename = 'bj' + stock.replace('.BJ', '')
        else:
            filename = stock
        
        # 创建新的DataFrame，使用中文列名
        new_df = df.copy()
        new_df['股票代码'] = filename  # 使用文件名作为股票代码
        new_df['股票名称'] = stock_dict.get(stock, '')
        new_df['交易日期'] = df.index.strftime('%Y/%m/%d')  # 格式化为2022/10/28格式
        new_df['开盘价'] = df['open']
        new_df['最高价'] = df['high']
        new_df['最低价'] = df['low']
        new_df['收盘价'] = df['close']
        new_df['前收盘价'] = df['preClose']
        new_df['成交量'] = df['volume']
        new_df['成交额'] = df['amount']
        
        # 只保留需要的列
        new_df = new_df[['股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价', '成交量', '成交额']]
        
        # 手动写入CSV文件，第一行为空
        with open(folder_path +'/' + filename + '.csv', 'w', encoding='utf-8-sig') as f:
            # 写入空行
            f.write('\n')
            # 写入列名
            f.write(','.join(new_df.columns) + '\n')
            # 写入数据
            for _, row in new_df.iterrows():
                f.write(','.join(str(x) for x in row) + '\n')
    except:
        pass
print('数据整理完成')
